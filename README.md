# 防火墙IP封禁管理工具

这是一个使用Python3和requests库模拟浏览器操作的防火墙IP管理工具，可以自动化执行登录、查询和添加封禁IP的操作。

## 功能特性

- **自动登录**: 模拟浏览器登录防火墙管理系统
- **查询封禁IP**: 获取当前已封禁的IP地址列表
- **添加封禁IP**: 向封禁列表中添加新的IP地址
- **批量操作**: 支持一次性添加多个IP地址
- **错误处理**: 完善的异常处理和状态检查

## 安装依赖

```bash
pip install -r requirements.txt
```

或者手动安装：

```bash
pip install requests urllib3
```

## 使用方法

### 1. 基本使用

```python
from firewall_manager import FirewallManager

# 创建管理器实例
fw = FirewallManager(host="*************", use_https=True)

# 登录
fw.login("username", "password")

# 查询当前封禁IP
current_ips = fw.query_banned_ips()
print(f"当前封禁IP: {current_ips}")

# 添加新IP
fw.add_banned_ip("*************", current_ips)
```

### 2. 一键管理（推荐）

```python
from firewall_manager import FirewallManager

fw = FirewallManager()

# 一键完成：登录 -> 查询 -> 添加
new_ips = ["*************", "*************"]
success = fw.manage_ips("username", "password", new_ips)
```

### 3. 运行示例

```bash
python example_usage.py
```

## 工作流程

1. **登录阶段**
   - 发送POST请求到 `/cgi/maincgi.cgi?Url=Index`
   - 提交用户名和密码
   - 获取session_id用于后续请求

2. **查询阶段**
   - 发送GET请求到主机对象编辑页面
   - 解析HTML响应中的JavaScript变量 `ip_str`
   - 提取当前封禁的IP地址列表

3. **添加阶段**
   - 构造包含现有IP和新IP的POST数据
   - 发送POST请求提交更新
   - 检查响应确认操作成功

## 配置说明

### FirewallManager 参数

- `host`: 防火墙主机地址（默认: "*************"）
- `use_https`: 是否使用HTTPS协议（默认: True）

### 登录信息

根据实际情况修改以下信息：

```python
username = "your_username"
password = "your_password"
```

## 注意事项

1. **SSL证书**: 脚本默认禁用SSL证书验证，适用于自签名证书环境
2. **编码处理**: 自动处理中文字符的URL编码
3. **会话管理**: 使用requests.Session()保持登录状态
4. **错误处理**: 包含完善的异常捕获和状态检查

## 安全建议

1. 不要在代码中硬编码密码
2. 建议从环境变量或配置文件读取敏感信息
3. 在生产环境中启用SSL证书验证

## 示例输出

```
开始防火墙IP管理流程...

1. 正在登录...
登录成功，获取到session_id: MTUxNTg0MzU5NzIzODI2

2. 正在查询当前封禁IP列表...
查询到当前封禁IP列表: ['*******', '*************', '***********', '***********', '*******']

3. 正在添加新IP地址: ['*******', '*******']
成功添加IP地址: *******
成功添加IP地址: *******

操作完成，成功添加 2/2 个IP地址

所有操作成功完成！
```

## 故障排除

1. **登录失败**: 检查用户名、密码和网络连接
2. **查询失败**: 确认session有效性和URL编码
3. **添加失败**: 检查IP格式和权限设置

## 文件说明

- `firewall_manager.py`: 主要的防火墙管理类
- `example_usage.py`: 使用示例和演示代码
- `requirements.txt`: Python依赖包列表
- `README.md`: 使用说明文档
