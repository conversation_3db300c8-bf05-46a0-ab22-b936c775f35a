POST /cgi/maincgi.cgi?Url=HostObj&Act=Edit&Name=01-%CD%F8%B0%B2%B7%E2%BD%FB%C4%DA%CD%F8%B5%D8%D6%B7 HTTP/1.1
Host: *************
Connection: keep-alive
Content-Length: 384
Cache-Control: max-age=0
sec-ch-ua: "Not_A Brand";v="99", "Google Chrome";v="109", "Chromium";v="109"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
Upgrade-Insecure-Requests: 1
Origin: https://*************
Content-Type: application/x-www-form-urlencoded
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9
Sec-Fetch-Site: same-origin
Sec-Fetch-Mode: navigate
Sec-Fetch-User: ?1
Sec-Fetch-Dest: frame
Referer: https://*************/cgi/maincgi.cgi?Url=HostObj&Act=Edit&Name=01-%CD%F8%B0%B2%B7%E2%BD%FB%C4%DA%CD%F8%B5%D8%D6%B7
Accept-Encoding: gzip, deflate, br
Accept-Language: zh-CN,zh;q=0.9
Cookie: session_id_443=MTUxNTg0MzU5NzIzODI2

def_host_name=01-%CD%F8%B0%B2%B7%E2%BD%FB%C4%DA%CD%F8%B5%D8%D6%B7&def_host_mac=00%3A00%3A00%3A00%3A00%3A00&def_host_ipad=*******&def_host_ipad=*************&def_host_ipad=***********&def_host_ipad=***********&def_host_ipad=*******&host_ipad_input=*******&name_hidden=01-%CD%F8%B0%B2%B7%E2%BD%FB%C4%DA%CD%F8%B5%D8%D6%B7&def_host_frompage=&def_host_from=&def_host_edt_but=+%C8%B7%B6%A8+


HTTP/1.1 200 OK
Date: Thu, 10 Jul 2025 02:49:04 GMT
Server: TOPSEC
Cache-Control: no-cache
Pragma: no-cache
X-Frame-Options: SAMEORIGIN
Keep-Alive: timeout=5, max=100
Connection: Keep-Alive
Content-Type: text/html; charset=gb2312
Content-Length: 799

<HTML>
<HEAD>
 <LINK REL=stylesheet HREF="/site/css/style.css" TYPE="text/css"/>
  <LINK REL=stylesheet HREF="/site/css/main.css" TYPE="text/css"/>
<SCRIPT SRC="/site/js/checkinput.js"></SCRIPT>
<SCRIPT SRC="/site/js/deleteelm.js"></SCRIPT>
<SCRIPT SRC="/site/js/function.js"></SCRIPT>
<SCRIPT SRC="/site/js/js_basement.js"></SCRIPT>
<SCRIPT type='text/javascript'  SRC="/site/js/sv_js.js"></SCRIPT>
<SCRIPT language="javascript">

function opCancel() {
	if (window.opener)
		window.close();	
	else
		history.back(1);
}
</SCRIPT>

</HEAD>
<BODY>
<script language="JavaScript">
<!--
// -->
</script>
			<div class="right_container tab_container">
<table class="tab_content" cellspacing="0"width="100%">
<tr><td style="padding:5px"><script> window.location='maincgi.cgi?Url=HostObj';</script>