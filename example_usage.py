#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
防火墙IP管理使用示例
"""

from firewall_manager import FirewallManager


def example_basic_usage():
    """基本使用示例"""
    print("=== 基本使用示例 ===")
    
    # 创建防火墙管理器
    fw = FirewallManager(host="*************", use_https=True)
    
    # 登录信息
    username = "wabsuper"
    password = "whgD@955989"
    
    # 1. 登录
    print("1. 登录中...")
    if fw.login(username, password):
        print("✓ 登录成功")
    else:
        print("✗ 登录失败")
        return
    
    # 2. 查询当前封禁IP
    print("\n2. 查询当前封禁IP...")
    current_ips = fw.query_banned_ips()
    print(f"当前封禁IP列表: {current_ips}")
    
    # 3. 添加新的封禁IP
    new_ip = "*******"
    print(f"\n3. 添加新IP: {new_ip}")
    if fw.add_banned_ip(new_ip, current_ips):
        print(f"✓ 成功添加IP: {new_ip}")
    else:
        print(f"✗ 添加IP失败: {new_ip}")


def example_batch_add():
    """批量添加IP示例"""
    print("\n=== 批量添加IP示例 ===")
    
    fw = FirewallManager()
    
    # 登录信息
    username = "wabsuper"
    password = "whgD@955989"
    
    # 要添加的IP列表
    new_ips = ["*******", "*******", "*******"]
    
    # 使用一键管理功能
    success = fw.manage_ips(username, password, new_ips)
    
    if success:
        print("✓ 批量添加成功")
    else:
        print("✗ 批量添加部分失败")


def example_step_by_step():
    """分步操作示例"""
    print("\n=== 分步操作示例 ===")
    
    fw = FirewallManager()
    
    try:
        # 步骤1：登录
        if not fw.login("wabsuper", "whgD@955989"):
            raise Exception("登录失败")
        
        # 步骤2：查询现有IP
        current_ips = fw.query_banned_ips()
        print(f"现有IP数量: {len(current_ips)}")
        
        # 步骤3：检查特定IP是否已存在
        check_ip = "*******"
        if check_ip in current_ips:
            print(f"IP {check_ip} 已在封禁列表中")
        else:
            print(f"IP {check_ip} 不在封禁列表中")
        
        # 步骤4：添加新IP（如果不存在）
        new_ip = "*******"
        if new_ip not in current_ips:
            if fw.add_banned_ip(new_ip, current_ips):
                print(f"成功添加新IP: {new_ip}")
            else:
                print(f"添加新IP失败: {new_ip}")
        else:
            print(f"IP {new_ip} 已存在，跳过添加")
            
    except Exception as e:
        print(f"操作过程中发生错误: {e}")


if __name__ == "__main__":
    # 运行所有示例
    # example_basic_usage()
    # example_batch_add()
    example_step_by_step()
    
    print("\n=== 所有示例执行完成 ===")
