#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
防火墙IP封禁管理脚本
模拟浏览器操作，实现登录、查询、添加封禁IP的功能
"""

import requests
import re
import urllib.parse
from typing import List, Optional


class FirewallManager:
    def __init__(self, host: str = "*************", use_https: bool = True):
        """
        初始化防火墙管理器
        
        Args:
            host: 防火墙主机地址
            use_https: 是否使用HTTPS
        """
        self.host = host
        self.protocol = "https" if use_https else "http"
        self.base_url = f"{self.protocol}://{self.host}"
        self.session = requests.Session()
        self.session_id = None
        
        # 设置请求头，模拟浏览器
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Cache-Control': 'max-age=0',
            'sec-ch-ua': '"Not_A Brand";v="99", "Google Chrome";v="109", "Chromium";v="109"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'Upgrade-Insecure-Requests': '1'
        })
        
        # 禁用SSL验证警告（如果使用自签名证书）
        requests.packages.urllib3.disable_warnings()
        self.session.verify = False

    def login(self, username: str, password: str) -> bool:
        """
        登录防火墙系统
        
        Args:
            username: 用户名
            password: 密码
            
        Returns:
            bool: 登录是否成功
        """
        try:
            # 准备登录数据
            login_data = {
                'username': username,
                'passwd': password,
                'loginSubmitIpt': ''
            }
            
            # 设置登录请求的特定头部
            login_headers = {
                'Content-Type': 'application/x-www-form-urlencoded',
                'Origin': self.base_url,
                'Referer': f'{self.base_url}/',
                'Sec-Fetch-Site': 'same-origin',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-User': '?1',
                'Sec-Fetch-Dest': 'document'
            }
            
            # 发送登录请求
            response = self.session.post(
                f'{self.base_url}/cgi/maincgi.cgi?Url=Index',
                data=login_data,
                headers=login_headers
            )
            
            # 检查响应状态
            if response.status_code == 200:
                # 从Set-Cookie头中提取session_id
                for cookie in response.cookies:
                    if cookie.name.startswith('session_id'):
                        self.session_id = cookie.value
                        print(f"登录成功，获取到session_id: {self.session_id}")
                        return True
                
                # 如果没有找到session_id，检查响应内容
                if 'window.location="maincgi.cgi?Url=Main"' in response.text:
                    print("登录成功，但未找到session_id")
                    return True
                else:
                    print("登录失败，请检查用户名和密码")
                    return False
            else:
                print(f"登录请求失败，状态码: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"登录过程中发生错误: {e}")
            return False

    def query_banned_ips(self) -> List[str]:
        """
        查询当前封禁的IP地址列表
        
        Returns:
            List[str]: IP地址列表
        """
        try:
            # 设置查询请求的头部
            query_headers = {
                'Sec-Fetch-Site': 'same-origin',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-User': '?1',
                'Sec-Fetch-Dest': 'frame',
                'Referer': f'{self.base_url}/cgi/maincgi.cgi?Url=HostObj'
            }
            
            # URL编码的主机对象名称
            encoded_name = "01-%CD%F8%B0%B2%B7%E2%BD%FB%C4%DA%CD%F8%B5%D8%D6%B7"
            
            # 发送查询请求
            response = self.session.get(
                f'{self.base_url}/cgi/maincgi.cgi?Url=HostObj&Act=Edit&Name={encoded_name}',
                headers=query_headers
            )
            
            if response.status_code == 200:
                # 从响应中提取IP地址字符串
                # 查找 var ip_str = '...' 这一行
                ip_pattern = r"var ip_str = '([^']*)';"
                match = re.search(ip_pattern, response.text)
                
                if match:
                    ip_str = match.group(1).strip()
                    # 分割IP地址字符串，过滤空字符串
                    ip_list = [ip.strip() for ip in ip_str.split() if ip.strip()]
                    print(f"查询到当前封禁IP列表: {ip_list}")
                    return ip_list
                else:
                    print("未找到IP地址信息")
                    return []
            else:
                print(f"查询请求失败，状态码: {response.status_code}")
                return []
                
        except Exception as e:
            print(f"查询过程中发生错误: {e}")
            return []

    def add_banned_ip(self, new_ip: str, current_ips: Optional[List[str]] = None) -> bool:
        """
        添加新的封禁IP地址
        
        Args:
            new_ip: 要添加的新IP地址
            current_ips: 当前的IP列表，如果为None则自动查询
            
        Returns:
            bool: 添加是否成功
        """
        try:
            # 如果没有提供当前IP列表，先查询
            if current_ips is None:
                current_ips = self.query_banned_ips()
            
            # 检查IP是否已存在
            if new_ip in current_ips:
                print(f"IP地址 {new_ip} 已存在于封禁列表中")
                return True
            
            # 准备POST数据
            post_data = {
                'def_host_name': '01-网安封禁内网地址',
                'def_host_mac': '00:00:00:00:00:00',
                'host_ipad_input': new_ip,
                'name_hidden': '01-网安封禁内网地址',
                'def_host_frompage': '',
                'def_host_from': '',
                'def_host_edt_but': ' 确定 '
            }
            
            # 添加所有现有的IP地址
            for ip in current_ips:
                post_data[f'def_host_ipad'] = ip
            
            # 设置POST请求头部
            post_headers = {
                'Content-Type': 'application/x-www-form-urlencoded',
                'Origin': self.base_url,
                'Referer': f'{self.base_url}/cgi/maincgi.cgi?Url=HostObj&Act=Edit&Name=01-%CD%F8%B0%B2%B7%E2%BD%FB%C4%DA%CD%F8%B5%D8%D6%B7',
                'Sec-Fetch-Site': 'same-origin',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-User': '?1',
                'Sec-Fetch-Dest': 'frame'
            }
            
            # URL编码的主机对象名称
            encoded_name = "01-%CD%F8%B0%B2%B7%E2%BD%FB%C4%DA%CD%F8%B5%D8%D6%B7"
            
            # 发送添加请求
            response = self.session.post(
                f'{self.base_url}/cgi/maincgi.cgi?Url=HostObj&Act=Edit&Name={encoded_name}',
                data=post_data,
                headers=post_headers
            )
            
            if response.status_code == 200:
                # 检查响应是否包含重定向脚本
                if "window.location='maincgi.cgi?Url=HostObj';" in response.text:
                    print(f"成功添加IP地址: {new_ip}")
                    return True
                else:
                    print("添加IP地址可能失败，请检查响应")
                    return False
            else:
                print(f"添加请求失败，状态码: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"添加IP过程中发生错误: {e}")
            return False

    def manage_ips(self, username: str, password: str, new_ips: List[str]) -> bool:
        """
        完整的IP管理流程：登录 -> 查询 -> 添加
        
        Args:
            username: 用户名
            password: 密码
            new_ips: 要添加的新IP地址列表
            
        Returns:
            bool: 操作是否成功
        """
        print("开始防火墙IP管理流程...")
        
        # 1. 登录
        print("\n1. 正在登录...")
        if not self.login(username, password):
            print("登录失败，终止操作")
            return False
        
        # 2. 查询当前IP列表
        print("\n2. 正在查询当前封禁IP列表...")
        current_ips = self.query_banned_ips()
        
        # 3. 添加新IP
        print(f"\n3. 正在添加新IP地址: {new_ips}")
        success_count = 0
        for new_ip in new_ips:
            if self.add_banned_ip(new_ip, current_ips):
                current_ips.append(new_ip)  # 更新本地列表
                success_count += 1
            else:
                print(f"添加IP {new_ip} 失败")
        
        print(f"\n操作完成，成功添加 {success_count}/{len(new_ips)} 个IP地址")
        return success_count == len(new_ips)


def main():
    """
    主函数 - 演示使用方法
    """
    # 创建防火墙管理器实例
    fw_manager = FirewallManager()
    
    # 配置登录信息
    username = "wabsuper"
    password = "whgD@955989"  # 注意：实际使用时应该从环境变量或配置文件读取
    
    # 要添加的新IP地址列表
    new_ips_to_add = ["*******", "*******"]
    
    # 执行完整的管理流程
    success = fw_manager.manage_ips(username, password, new_ips_to_add)
    
    if success:
        print("\n所有操作成功完成！")
    else:
        print("\n部分操作失败，请检查日志")


if __name__ == "__main__":
    main()
